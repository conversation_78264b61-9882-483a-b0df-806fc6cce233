---
// Product Image Component - Enhanced with optimized image loading
import OptimizedImage from '../OptimizedImage.astro';

export interface Props {
  name: string;
  firstImage?: string;
  imageCount: number;
  hasMultipleImages: boolean;
  hasDefects: boolean;
  index: number;
  slug: string;
}

const {
  name,
  firstImage,
  imageCount,
  hasMultipleImages,
  hasDefects,
  index
} = Astro.props;

// Determine if this is an external image (Unsp<PERSON>, Bunny CDN, etc.)
const isExternal = firstImage?.startsWith('http') || firstImage?.startsWith('//');
const isBunnyCDN = firstImage?.includes('bunnycdn.com') || firstImage?.includes('b-cdn.net');
---

<div class="product-image-container">
  {firstImage ? (
    <OptimizedImage
      src={firstImage}
      alt={name}
      class="product-image"
      loading={index < 2 ? "eager" : "lazy"}
      width={400}
      height={300}
      fetchpriority={index === 0 ? "high" : "auto"}
      sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
      cdnOptimization={isExternal}
      cdnTransforms={isBunnyCDN ? "quality=85&format=webp&fit=cover" : undefined}
      fallbackSrc="/images/product-placeholder.svg"
    />
  ) : (
    <div class="product-image-placeholder" aria-label="No image available">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
      </svg>
    </div>
  )}
  
  {hasMultipleImages && (
    <div class="image-count-badge" aria-label={`${imageCount} images`}>
      <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z"/>
      </svg>
      {imageCount}
    </div>
  )}
  
  {hasDefects && (
    <div class="defects-badge" title="Has defects" aria-label="Product has defects">
      <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12,2L1,21H23M12,6L19.53,19H4.47M11,10V14H13V10M11,16V18H13V16"/>
      </svg>
    </div>
  )}

  <!-- Watermark text -->
  <div class="watermark" aria-hidden="true">
    CheersMarketplace.com
  </div>
</div>

<style>
  .product-image-container {
    position: relative;
    width: 100%;
    aspect-ratio: 5/4;
    overflow: hidden;
    border-radius: var(--radius-lg);
    background: var(--light-background);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .product-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--text-secondary);
    background: var(--border-light);
  }

  .image-count-badge,
  .defects-badge {
    position: absolute;
    top: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    backdrop-filter: blur(4px);
  }

  .image-count-badge {
    right: 0.5rem;
  }

  .defects-badge {
    left: 0.5rem;
    background: rgba(220, 38, 38, 0.9);
  }

  /* Watermark styling */
  .watermark {
    position: absolute;
    bottom: 0.5rem;
    left: 0.5rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.25rem 0.375rem;
    border-radius: 0.25rem;
    opacity: 0.8;
    pointer-events: none;
    z-index: 2;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: 0.025em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
  }

  /* Hover effects */
  .product-image-container:hover .product-image {
    transform: scale(1.05);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .image-count-badge,
    .defects-badge {
      top: 0.375rem;
      padding: 0.1875rem 0.375rem;
      font-size: 0.6875rem;
    }

    .image-count-badge {
      right: 0.375rem;
    }

    .defects-badge {
      left: 0.375rem;
    }

    .watermark {
      bottom: 0.375rem;
      left: 0.375rem;
      font-size: 0.5rem;
      padding: 0.1875rem 0.25rem;
    }
  }
</style>
